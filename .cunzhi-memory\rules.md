# 开发规范和规则

- 项目需要集成 ezuikit-js 支持 ezopen:// 协议视频播放，AccessToken 通过 /api/ys/get_access_token 接口获取，在施工记录详情的视频回放模块中实现
- 修复卡片管理模块mock数据路径问题：1.问题原因：postRequest函数会自动添加VITE_APP_BASE_URL前缀，导致实际请求路径与mock路径不匹配；2.解决方案：API服务中使用/card/get_ls格式（带前缀斜杠），mock文件中对应使用POST /card/get_ls格式；3.参考静态IP管理模块的实现方式，保持项目API路径的一致性；4.重要：使用npm run start启动项目以启用mock功能，避免使用start:dev（包含MOCK=none参数）
- 修复卡片管理模块最近访问记录问题：在src/components/SmartBreadcrumb/index.tsx的pathToTitleMap映射表中添加了卡片管理相关路径：/system/cardmanagement(卡片管理)、/system/cardmanagement/add(添加卡片)、/system/cardmanagement/edit(编辑卡片)。这样用户访问卡片管理页面时，会自动记录到最近访问历史中，支持快速导航功能
